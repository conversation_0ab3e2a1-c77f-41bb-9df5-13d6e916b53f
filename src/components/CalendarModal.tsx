import React, { useEffect } from 'react';
import { Di<PERSON>, DialogContent, Dialog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { X } from 'lucide-react';

interface CalendarModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CalendarModal: React.FC<CalendarModalProps> = ({ isOpen, onClose }) => {
  useEffect(() => {
    if (isOpen) {
      // Load the GHL calendar script when modal opens
      const script = document.createElement('script');
      script.src = 'https://calendar.aha-innovations.com/js/form_embed.js';
      script.type = 'text/javascript';
      script.async = true;
      document.head.appendChild(script);

      return () => {
        // Clean up script when modal closes
        const existingScript = document.querySelector('script[src="https://calendar.aha-innovations.com/js/form_embed.js"]');
        if (existingScript) {
          document.head.removeChild(existingScript);
        }
      };
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-full h-[80vh] p-0 overflow-hidden">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white">
              Schedule a Consultation
            </DialogTitle>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </DialogHeader>
        
        <div className="flex-1 p-6 pt-4">
          <div className="w-full h-full">
            <iframe
              src="https://calendar.aha-innovations.com/widget/booking/muwcL091TjKbXukSZgs5"
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                overflow: 'hidden',
                borderRadius: '8px'
              }}
              scrolling="no"
              id="eLsfI5xBSm4w9nZyxtfy_1748977986939"
              title="Schedule Consultation"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CalendarModal;
