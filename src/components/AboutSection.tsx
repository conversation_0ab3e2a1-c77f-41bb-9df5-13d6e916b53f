
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

const AboutSection = () => {
  return (
    <section id="about" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-white mb-4 sm:mb-6">
            About <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Millennial Business Innovations</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            We believe in the power of technology to drive business growth. Our team of experienced developers, designers, and strategists work tirelessly to turn your ideas into actionable, scalable digital solutions.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
          <div className="space-y-4 sm:space-y-6">
            <Card className="bg-white/5 border border-white/10 backdrop-blur-lg hover:bg-white/10 transition-all duration-300">
              <CardContent className="p-4 sm:p-6">
                <h3 className="font-montserrat font-semibold text-xl sm:text-2xl text-white mb-3 sm:mb-4">Our Mission</h3>
                <p className="font-poppins text-gray-300 text-sm sm:text-base">
                  Our mission is to empower businesses with cutting-edge technology, enabling them to reach new heights and achieve their goals. We transform ideas into digital reality.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/5 border border-white/10 backdrop-blur-lg hover:bg-white/10 transition-all duration-300">
              <CardContent className="p-4 sm:p-6">
                <h3 className="font-montserrat font-semibold text-xl sm:text-2xl text-white mb-3 sm:mb-4">Our Vision</h3>
                <p className="font-poppins text-gray-300 text-sm sm:text-base">
                  To be the trusted partner for businesses looking to leverage technology for growth, creating innovative solutions that drive success in the digital age.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6 sm:space-y-8">
            <div className="text-center">
              <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto mb-4 sm:mb-6 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-4xl sm:text-6xl">
                🚀
              </div>
              <h3 className="font-montserrat font-semibold text-xl sm:text-2xl text-white mb-3 sm:mb-4">Innovation First</h3>
              <p className="font-poppins text-gray-300 text-sm sm:text-base">
                We stay ahead of the curve, utilizing the latest technologies and methodologies to deliver exceptional results.
              </p>
            </div>

            <div className="grid grid-cols-3 gap-2 sm:gap-4 text-center">
              <div className="p-2 sm:p-4">
                <div className="text-2xl sm:text-3xl font-montserrat font-bold text-primary mb-1 sm:mb-2">50+</div>
                <div className="text-xs sm:text-sm font-poppins text-gray-400">Projects Completed</div>
              </div>
              <div className="p-2 sm:p-4">
                <div className="text-2xl sm:text-3xl font-montserrat font-bold text-accent mb-1 sm:mb-2">98%</div>
                <div className="text-xs sm:text-sm font-poppins text-gray-400">Client Satisfaction</div>
              </div>
              <div className="p-2 sm:p-4">
                <div className="text-2xl sm:text-3xl font-montserrat font-bold text-secondary mb-1 sm:mb-2">24/7</div>
                <div className="text-xs sm:text-sm font-poppins text-gray-400">Support Available</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
