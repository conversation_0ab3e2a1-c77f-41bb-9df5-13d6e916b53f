
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight, Calendar } from 'lucide-react';

const HeroSection = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const techStack = [
    { name: 'React', icon: '⚛️' },
    { name: 'Next.js', icon: '▲' },
    { name: 'TypeScript', icon: 'TS' },
    { name: 'Node.js', icon: '🟢' },
    { name: 'Python', icon: '🐍' },
    { name: 'MongoDB', icon: '🍃' },
    { name: 'PostgreSQL', icon: '🐘' },
    { name: 'AWS', icon: '☁️' },
    { name: 'Docker', icon: '🐳' },
    { name: 'Figma', icon: '🎨' }
  ];

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black"></div>
      
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-1/4 left-1/3 w-48 h-48 bg-secondary/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>
      
      {/* Glowing orb at bottom */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-96 h-24 bg-gradient-to-t from-white/20 to-transparent rounded-full blur-2xl"></div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        {/* New badge */}
        <div
          className="inline-flex items-center px-4 py-2 rounded-full bg-primary/20 border border-primary/30 text-primary text-sm font-poppins mb-8 animate-fade-in-up cursor-pointer hover:bg-primary/30 transition-colors duration-300"
          onClick={() => window.open('https://site.aha-innovations.com', '_blank')}
        >
          <span className="bg-primary text-white px-2 py-1 rounded-full text-xs mr-3">New</span>
          Aha-Innovations, your all in one platform is now live
          <ArrowRight className="ml-2 w-4 h-4" />
        </div>
        
        <h1 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl text-white mb-6 animate-fade-in-up leading-tight" style={{animationDelay: '0.2s'}}>
          We help founders turn ideas
          <br className="hidden sm:block" />
          <span className="block sm:inline">into seamless </span><span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text italic">digital experiences</span>
        </h1>

        <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-300 mb-8 sm:mb-12 max-w-3xl mx-auto animate-fade-in-up px-4 sm:px-0" style={{animationDelay: '0.4s'}}>
          Hello, We're <span className="text-white font-semibold">Millennial Business Innovations</span> 🚀 a Full Stack Developer
        </p>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 animate-fade-in-up px-4 sm:px-0" style={{animationDelay: '0.6s'}}>
          <Button
            onClick={() => scrollToSection('contact')}
            className="w-full sm:w-auto bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-6 sm:px-8 py-3 sm:py-4 rounded-full text-base sm:text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-primary/25 animate-glow-pulse"
          >
            Let's Connect
            <ArrowRight className="ml-2 w-4 sm:w-5 h-4 sm:h-5" />
          </Button>

          <Button
            variant="ghost"
            onClick={() => scrollToSection('services')}
            className="w-full sm:w-auto text-white border border-white/20 hover:bg-white/10 font-poppins px-6 sm:px-8 py-3 sm:py-4 rounded-full text-base sm:text-lg backdrop-blur-sm"
          >
            <Calendar className="mr-2 w-4 sm:w-5 h-4 sm:h-5" />
            View Our Services
          </Button>
        </div>

        {/* Tech Stack Section */}
        <div className="animate-fade-in-up px-4 sm:px-0" style={{animationDelay: '0.8s'}}>
          <p className="text-gray-400 text-xs sm:text-sm font-poppins mb-4 sm:mb-6 uppercase tracking-wider">
            Built with the tools you love
          </p>
          <div className="flex flex-wrap justify-center items-center gap-3 sm:gap-4 md:gap-6 max-w-4xl mx-auto">
            {techStack.map((tech, index) => (
              <div
                key={tech.name}
                className="flex flex-col items-center group cursor-pointer"
                style={{animationDelay: `${0.9 + index * 0.1}s`}}
              >
                <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 flex items-center justify-center text-xl sm:text-2xl transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-lg animate-fade-in-up">
                  {tech.icon}
                </div>
                <span className="text-xs text-gray-400 mt-1 sm:mt-2 font-poppins group-hover:text-white transition-colors duration-300">
                  {tech.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
