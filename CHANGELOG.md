# Changelog

All notable changes to the Millennial Business Innovations website will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.1] - 2024-12-19

### Fixed
- **Mobile Header Overlap Issue**
  - Fixed navigation header overlapping with announcement banner on real mobile devices
  - Added proper top padding (pt-20 sm:pt-24) to hero section to account for fixed navigation
  - Enhanced navigation background with semi-transparent backdrop for better visibility
  - Improved mobile navigation spacing and backdrop blur effects

### Added
- **GHL Calendar Integration**
  - Created CalendarModal component for seamless calendar booking
  - Integrated GoHighLevel calendar widget (https://calendar.aha-innovations.com/widget/booking/muwcL091TjKbXukSZgs5)
  - Added calendar modal to both Navigation and HeroSection components
  - Professional calendar popup with proper styling and responsive design

### Changed
- **Enhanced User Experience**
  - Updated all "Book a Call" buttons to open calendar modal instead of scrolling to contact section
  - Improved navigation transparency and backdrop effects
  - Better mobile menu interaction with calendar integration
  - Calendar modal automatically loads GHL embed script when opened

### Technical Details
- **New Component**: `src/components/CalendarModal.tsx`
- **Calendar Integration**: Uses iframe embed with dynamic script loading
- **Modal System**: Built with shadcn/ui Dialog component
- **Responsive Design**: Calendar modal adapts to different screen sizes
- **Clean Script Management**: Automatic script cleanup when modal closes

## [1.1.0] - 2024-12-19

### Added
- **Complete Theme System Implementation**
  - Light/Dark/System theme toggle with persistent storage
  - Custom ThemeProvider component with React Context
  - ThemeToggle dropdown component with sun/moon icons
  - Theme toggle available in both desktop and mobile navigation
  - Automatic system theme detection and preference following

- **Comprehensive Theme-Aware Styling**
  - All sections now properly support both light and dark modes
  - Smooth transitions between theme changes
  - Proper contrast ratios for accessibility compliance
  - CSS variables integration with Tailwind's dark mode system

### Changed
- **Hero Section Content Updates**
  - Main heading: "We help founders turn ideas into seamless digital experiences" → "You dream it. We build it."
  - Subtext: "Hello, We're Millennial Business Innovations 🚀 a Full Stack Developer" → "Helping founders launch MVPs and digital products without the tech overwhelm."
  - Greeting: Added "👋 Hello, we're Millennial Business Innovations your full-stack product team." (removed em dash)
  - Applied gradient typography to "digital products" in subtext

- **Theme-Aware Component Updates**
  - **HeroSection**: Light gray gradients, adaptive text colors, theme-aware buttons
  - **AboutSection**: Light backgrounds, semi-transparent cards, adaptive text
  - **ServicesSection**: Light mode backgrounds, card styling updates
  - **PortfolioSection**: Adaptive backgrounds, border color updates
  - **PricingSection**: Light mode card styling, button color adaptations
  - **ContactSection**: Form input styling, card background updates
  - **Footer**: Light mode backgrounds, adaptive text and link colors
  - **Navigation**: Adaptive navigation bar, mobile menu theme support

- **CSS Variables Enhancement**
  - Updated light mode background from pure white to light gray (98%)
  - Enhanced dark mode with true black backgrounds
  - Improved color contrast for better readability

### Technical Details
- **Theme Storage**: Uses localStorage with key "mbi-ui-theme"
- **Default Theme**: System preference detection
- **CSS Framework**: Tailwind CSS with class-based dark mode
- **State Management**: React Context API for theme state
- **Hot Module Reload**: All changes support HMR for development

### Files Modified
- `src/components/ThemeProvider.tsx` (new)
- `src/components/ThemeToggle.tsx` (new)
- `src/App.tsx`
- `src/components/Navigation.tsx`
- `src/components/HeroSection.tsx`
- `src/components/AboutSection.tsx`
- `src/components/ServicesSection.tsx`
- `src/components/PortfolioSection.tsx`
- `src/components/PricingSection.tsx`
- `src/components/ContactSection.tsx`
- `src/components/Footer.tsx`
- `src/pages/Index.tsx`
- `src/index.css`

### Dependencies
- Existing `next-themes` package utilized for theme management
- No new dependencies added

## [1.0.0] - 2024-12-19

### Added
- Initial website launch
- Hero section with company branding
- About section with mission and vision
- Services showcase
- Portfolio gallery
- Pricing plans
- Contact form
- Footer with company information
- Responsive design for all screen sizes
- Modern UI with Tailwind CSS and shadcn/ui components

### Features
- Single-page application with smooth scrolling navigation
- Contact form with validation
- Responsive grid layouts
- Modern typography with Montserrat and Poppins fonts
- Gradient backgrounds and animations
- Mobile-first responsive design
